
import { useState } from 'react';
import { Button } from "@/components/ui/button";

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      company: "TechStart",
      text: "Fábio transformou completamente nossa presença digital. O design criado superou todas as expectativas e aumentou significativamente nossa conversão.",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"
    },
    {
      id: 2,
      name: "<PERSON>",
      company: "Inovação Digital",
      text: "Profissional excepcional! Entendeu perfeitamente nossa visão e entregou um projeto que elevou nossa marca a outro patamar.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
    },
    {
      id: 3,
      name: "<PERSON>",
      company: "Creative Studio",
      text: "O trabalho do Fábio é simplesmente incrível. Atenção aos detalhes, criatividade e profissionalismo definem seu trabalho.",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const current = testimonials[currentTestimonial];

  return (
    <section className="py-16 px-4 bg-dark-bg">
      <div className="container mx-auto max-w-4xl">
        <h2 className="font-gt-flexa text-neon-solar text-4xl md:text-5xl text-center mb-12">
          DEPOIMENTOS
        </h2>
        
        <div className="bg-gray-900 rounded-2xl p-8 md:p-12 text-center">
          <div className="mb-8">
            <img
              src={current.image}
              alt={current.name}
              className="w-20 h-20 rounded-full mx-auto mb-6 border-2 border-tomato-red"
              loading="lazy"
            />
            <blockquote className="text-text-white text-lg md:text-xl leading-relaxed mb-6 font-space-grotesk">
              "{current.text}"
            </blockquote>
            <div>
              <p className="font-gt-flexa text-neon-solar text-lg">
                {current.name}
              </p>
              <p className="text-border-gray">
                {current.company}
              </p>
            </div>
          </div>
          
          <div className="flex justify-center space-x-4">
            <Button
              onClick={prevTestimonial}
              variant="outline"
              className="border-neon-solar text-neon-solar hover:bg-neon-solar hover:text-dark-bg cursor-custom"
            >
              ←
            </Button>
            <Button
              onClick={nextTestimonial}
              variant="outline"
              className="border-neon-solar text-neon-solar hover:bg-neon-solar hover:text-dark-bg cursor-custom"
            >
              →
            </Button>
          </div>
          
          <div className="flex justify-center space-x-2 mt-6">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-300 cursor-custom ${
                  index === currentTestimonial ? 'bg-neon-solar' : 'bg-border-gray'
                }`}
                aria-label={`Depoimento ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
