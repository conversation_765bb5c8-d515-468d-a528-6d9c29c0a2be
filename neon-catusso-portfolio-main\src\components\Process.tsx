
import { Search, Pen, Circle, Check } from 'lucide-react';

const Process = () => {
  const steps = [
    {
      icon: <Search className="w-8 h-8" strokeWidth={2} />,
      title: "Descobrir",
      description: "Entendo seu negócio e objetivos"
    },
    {
      icon: <Pen className="w-8 h-8" strokeWidth={2} />,
      title: "Esboçar", 
      description: "Criação de wireframes e conceitos"
    },
    {
      icon: <Circle className="w-8 h-8" strokeWidth={2} />,
      title: "Refinar",
      description: "Desenvolvimento visual e testes"
    },
    {
      icon: <Check className="w-8 h-8" strokeWidth={2} />,
      title: "Entregar",
      description: "Finalização e implementação"
    }
  ];

  return (
    <section className="py-16 px-4 bg-dark-bg">
      <div className="container mx-auto">
        <h2 className="font-gt-flexa text-neon-solar text-4xl md:text-5xl text-center mb-12">
          MEU PROCESSO
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
          {steps.map((step, index) => (
            <div
              key={index}
              className="bg-neon-solar text-dark-bg p-8 rounded-lg text-center space-y-4 aspect-square flex flex-col justify-center items-center hover:scale-105 transition-transform duration-300 cursor-custom"
            >
              <div className="flex justify-center mb-4">
                {step.icon}
              </div>
              <h3 className="font-gt-flexa text-xl font-bold">
                {step.title}
              </h3>
              <p className="text-sm font-space-grotesk">
                {step.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Process;
