
import { useState } from 'react';
import Navigation from "@/components/Navigation";
import ThemeToggle from "@/components/ThemeToggle";
import { Button } from "@/components/ui/button";

const Contato = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    service: '',
    budget: '',
    message: ''
  });

  const faqs = [
    {
      question: "Qual é o prazo médio dos projetos?",
      answer: "O prazo varia conforme a complexidade, mas geralmente entre 2-6 semanas. Durante o briefing, defino um cronograma detalhado para seu projeto específico."
    },
    {
      question: "Como funciona o pagamento?",
      answer: "Trabalho com 50% na aprovação da proposta e 50% na entrega final. Para projetos maiores, posso dividir em mais parcelas conforme as entregas."
    },
    {
      question: "Você faz revisões no projeto?",
      answer: "Sim! Cada pacote inclui rodadas de revisão. Normalmente são 2-3 rounds, dependendo do serviço contratado."
    },
    {
      question: "Trabalha com empresas de outros estados?",
      answer: "Claro! Trabalho 100% remotamente e já atendi clientes de todo o Brasil. Utilizamos ferramentas digitais para uma comunicação eficiente."
    }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Aqui integraria com backend
    console.log('Form submitted:', formData);
    alert('Mensagem enviada! Respondo em até 24h.');
  };

  const openWhatsApp = () => {
    window.open('https://wa.me/5511999999999?text=Olá! Gostaria de agendar um briefing.', '_blank');
  };

  return (
    <div className="min-h-screen bg-white dark:bg-dark-bg">
      <Navigation />
      <ThemeToggle />
      
      <div className="container mx-auto px-4 py-16 md:pl-32">
        <header className="mb-12">
          <h1 className="font-gt-flexa text-4xl md:text-6xl text-dark-bg dark:text-neon-solar mb-4">
            CONTATO
          </h1>
          <p className="text-lg text-dark-bg dark:text-text-white max-w-2xl">
            Vamos conversar sobre seu projeto? Preencha o formulário ou me chame no WhatsApp.
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block font-gt-flexa text-sm text-dark-bg dark:text-text-white mb-2">
                    NOME *
                  </label>
                  <input
                    type="text"
                    name="name"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-dark-bg dark:border-neon-solar bg-white dark:bg-dark-bg text-dark-bg dark:text-text-white focus:outline-none focus:border-neon-solar"
                  />
                </div>
                <div>
                  <label className="block font-gt-flexa text-sm text-dark-bg dark:text-text-white mb-2">
                    EMAIL *
                  </label>
                  <input
                    type="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-dark-bg dark:border-neon-solar bg-white dark:bg-dark-bg text-dark-bg dark:text-text-white focus:outline-none focus:border-neon-solar"
                  />
                </div>
              </div>

              <div>
                <label className="block font-gt-flexa text-sm text-dark-bg dark:text-text-white mb-2">
                  EMPRESA
                </label>
                <input
                  type="text"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  className="w-full p-3 border-2 border-dark-bg dark:border-neon-solar bg-white dark:bg-dark-bg text-dark-bg dark:text-text-white focus:outline-none focus:border-neon-solar"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block font-gt-flexa text-sm text-dark-bg dark:text-text-white mb-2">
                    SERVIÇO
                  </label>
                  <select
                    name="service"
                    value={formData.service}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-dark-bg dark:border-neon-solar bg-white dark:bg-dark-bg text-dark-bg dark:text-text-white focus:outline-none focus:border-neon-solar cursor-custom"
                  >
                    <option value="">Selecione um serviço</option>
                    <option value="ux-ui">UX/UI Design</option>
                    <option value="branding">Branding Digital</option>
                    <option value="design-system">Design System</option>
                    <option value="consultoria">Consultoria</option>
                  </select>
                </div>
                <div>
                  <label className="block font-gt-flexa text-sm text-dark-bg dark:text-text-white mb-2">
                    ORÇAMENTO
                  </label>
                  <select
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-dark-bg dark:border-neon-solar bg-white dark:bg-dark-bg text-dark-bg dark:text-text-white focus:outline-none focus:border-neon-solar cursor-custom"
                  >
                    <option value="">Faixa de investimento</option>
                    <option value="2k-5k">R$ 2k - 5k</option>
                    <option value="5k-10k">R$ 5k - 10k</option>
                    <option value="10k-20k">R$ 10k - 20k</option>
                    <option value="20k+">R$ 20k+</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block font-gt-flexa text-sm text-dark-bg dark:text-text-white mb-2">
                  MENSAGEM *
                </label>
                <textarea
                  name="message"
                  required
                  rows={5}
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Conte mais sobre seu projeto..."
                  className="w-full p-3 border-2 border-dark-bg dark:border-neon-solar bg-white dark:bg-dark-bg text-dark-bg dark:text-text-white focus:outline-none focus:border-neon-solar resize-none"
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-neon-solar text-dark-bg hover:bg-yellow-400 font-gt-flexa text-lg py-3 cursor-custom"
              >
                ENVIAR MENSAGEM
              </Button>
            </form>

            <div className="mt-8 text-center">
              <p className="text-dark-bg dark:text-text-white mb-4">Ou me chame diretamente:</p>
              <Button
                onClick={openWhatsApp}
                className="bg-tomato-red text-text-white hover:bg-red-600 font-gt-flexa px-8 py-3 cursor-custom"
              >
                WHATSAPP
              </Button>
            </div>
          </div>

          {/* FAQ Section */}
          <div>
            <h2 className="font-gt-flexa text-2xl text-dark-bg dark:text-neon-solar mb-6">
              PERGUNTAS FREQUENTES
            </h2>
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <div
                  key={index}
                  className="border-2 border-dark-bg dark:border-neon-solar p-4 bg-white dark:bg-dark-bg"
                >
                  <h3 className="font-gt-flexa text-sm text-dark-bg dark:text-neon-solar mb-2">
                    {faq.question}
                  </h3>
                  <p className="text-dark-bg dark:text-text-white text-sm">
                    {faq.answer}
                  </p>
                </div>
              ))}
            </div>

            {/* Contact Info */}
            <div className="mt-8 bg-gray-100 dark:bg-dark-bg border-2 border-neon-solar p-6">
              <h3 className="font-gt-flexa text-lg text-dark-bg dark:text-neon-solar mb-4">
                INFORMAÇÕES DE CONTATO
              </h3>
              <div className="space-y-2 text-dark-bg dark:text-text-white">
                <p>📧 <EMAIL></p>
                <p>📱 (11) 99999-9999</p>
                <p>📍 São Paulo, SP</p>
                <p>⏰ Seg-Sex: 9h às 18h</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contato;
