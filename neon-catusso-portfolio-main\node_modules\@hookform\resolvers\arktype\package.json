{"name": "@hookform/resolvers/arktype", "amdName": "hookformResolversArktype", "version": "2.0.0", "private": true, "description": "React Hook Form validation resolver: arktype", "main": "dist/arktype.js", "module": "dist/arktype.module.js", "umd:main": "dist/arktype.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.0.0", "@hookform/resolvers": "^2.0.0", "arktype": "2.0.0-dev.14"}}