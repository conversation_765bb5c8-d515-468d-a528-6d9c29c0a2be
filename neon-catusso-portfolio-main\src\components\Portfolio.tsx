
import { useState, useEffect, useRef } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

const Portfolio = () => {
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalProgress, setModalProgress] = useState(0);
  const modalRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);

  const projects = [
    {
      id: 1,
      title: "Concordia",
      category: "Branding",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop",
      challenge: "Criar uma identidade visual moderna para uma empresa de consultoria financeira estabelecida há 20 anos.",
      process: "Pesquisa de mercado › Análise de concorrentes › Sketches › Prototipagem › Testes de usabilidade › Refinamento.",
      impact: "Aumento de 40% na captação de novos clientes e reconhecimento como marca líder no setor."
    },
    {
      id: 2,
      title: "Landing Page",
      category: "Web Design",
      image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=300&fit=crop",
      challenge: "Desenvolver uma landing page de alta conversão para um produto SaaS inovador.",
      process: "Análise de dados › Wireframes › Design system › Desenvolvimento › A/B testing › Otimização.",
      impact: "Taxa de conversão de 8.5%, superando a média do setor em 150%."
    },
    {
      id: 3,
      title: "Energia Solar",
      category: "Branding",
      image: "https://images.unsplash.com/photo-1497604401993-f2e922e5cb0a?w=400&h=300&fit=crop",
      challenge: "Posicionar uma startup de energia solar como líder em sustentabilidade.",
      process: "Brand strategy › Logo design › Manual de marca › Aplicações › Campanha de lançamento.",
      impact: "Reconhecimento nacional e aumento de 200% nas vendas no primeiro trimestre."
    },
    {
      id: 4,
      title: "Cokaki",
      category: "App Design",
      image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=300&fit=crop",
      challenge: "Criar um app de delivery com experiência gamificada e intuitiva.",
      process: "User research › Personas › Journey mapping › Wireframes › Protótipo › Testes de usabilidade.",
      impact: "App Store rating 4.8/5 e 50k downloads na primeira semana."
    },
    {
      id: 5,
      title: "App de Saúde",
      category: "Mobile App",
      image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop",
      challenge: "Desenvolver interface para telemedicina acessível a todas as idades.",
      process: "Pesquisa com usuários › Arquitetura de informação › Design inclusivo › Prototipagem › Validação.",
      impact: "10k pacientes atendidos mensalmente e prêmio de melhor app de saúde 2023."
    },
    {
      id: 6,
      title: "Blog Criativo",
      category: "Web Design",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop",
      challenge: "Redesign completo de blog para aumentar tempo de permanência dos leitores.",
      process: "Auditoria de UX › Heatmaps › Novo layout › Otimização de performance › Implementação.",
      impact: "Aumento de 65% no tempo de sessão e 30% mais páginas por visita."
    }
  ];

  const openModal = (projectId: number) => {
    setSelectedProject(projectId);
    setIsModalOpen(true);
    document.body.style.overflow = 'hidden';

    // Update progress bar
    const currentIndex = projects.findIndex(p => p.id === projectId);
    setModalProgress(((currentIndex + 1) / projects.length) * 100);

    // Focus trap - focus close button after modal opens
    setTimeout(() => {
      closeButtonRef.current?.focus();
    }, 100);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedProject(null);
    document.body.style.overflow = 'unset';
    setModalProgress(0);
  };

  const navigateProject = (direction: 'prev' | 'next') => {
    if (selectedProject === null) return;

    const currentIndex = projects.findIndex(p => p.id === selectedProject);
    let newIndex;

    if (direction === 'prev') {
      newIndex = currentIndex === 0 ? projects.length - 1 : currentIndex - 1;
    } else {
      newIndex = currentIndex === projects.length - 1 ? 0 : currentIndex + 1;
    }

    setSelectedProject(projects[newIndex].id);

    // Update progress bar
    setModalProgress(((newIndex + 1) / projects.length) * 100);
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isModalOpen) return;

      if (e.key === 'Escape') {
        closeModal();
      } else if (e.key === 'ArrowLeft') {
        navigateProject('prev');
      } else if (e.key === 'ArrowRight') {
        navigateProject('next');
      } else if (e.key === 'Tab') {
        // Focus trap - keep focus within modal
        e.preventDefault();
        const focusableElements = modalRef.current?.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (focusableElements && focusableElements.length > 0) {
          const firstElement = focusableElements[0] as HTMLElement;
          const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              lastElement.focus();
            }
          } else {
            if (document.activeElement === lastElement) {
              firstElement.focus();
            }
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isModalOpen, selectedProject]);

  const currentProject = projects.find(p => p.id === selectedProject);

  return (
    <>
      <section id="portfolio" className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <h2 className="font-gt-flexa text-dark-bg text-4xl md:text-5xl text-center mb-12">
            MEU TRABALHO
          </h2>

          {/* Masonry Grid */}
          <div className="columns-1 md:columns-2 lg:columns-3 gap-6 max-w-6xl mx-auto space-y-6">
            {projects.map((project) => (
              <div
                key={project.id}
                className="break-inside-avoid cursor-custom"
                onClick={() => openModal(project.id)}
              >
                <div className="w-80 h-56 border-2 border-dark-bg rounded bg-white overflow-hidden transition-all duration-200 hover:translate-y-1 hover:shadow-[0_4px_0_#FF3B30] group">
                  <div className="relative w-full h-full">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-full h-full object-cover transition-all duration-300"
                      style={{
                        filter: 'sepia(1) saturate(2) hue-rotate(180deg) brightness(0.8) contrast(1.2)'
                      }}
                      loading="lazy"
                      decoding="async"
                    />
                    <div className="absolute bottom-0 left-0 bg-tomato-red bg-opacity-80 text-text-white text-xs px-2 py-1">
                      {project.category}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Upgraded Project Modal */}
      {isModalOpen && currentProject && (
        <div
          className="fixed inset-0 z-50 bg-black bg-opacity-80 flex items-center justify-center p-4"
          role="dialog"
          aria-label={currentProject.title}
          aria-modal="true"
        >
          {/* Modal content with slide-up + fade animation */}
          <div
            ref={modalRef}
            className="relative w-[90vw] md:w-full md:max-w-[760px] bg-dark-bg rounded-lg overflow-hidden modal-slide-up"
            style={{ backgroundColor: '#0F0F1C' }}
          >
            {/* Close button */}
            <button
              ref={closeButtonRef}
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 text-neon-solar hover:text-tomato-red transition-colors p-2 bg-dark-bg rounded-full"
              aria-label="Fechar modal"
            >
              <X size={24} />
            </button>

            {/* Navigation arrows - fixed sides with glow */}
            <button
              onClick={() => navigateProject('prev')}
              className="fixed left-4 top-1/2 -translate-y-1/2 text-neon-solar hover:text-tomato-red transition-colors p-2 nav-arrow-glow"
              aria-label="Projeto anterior"
              style={{ zIndex: 60 }}
            >
              <ChevronLeft size={32} />
            </button>

            <button
              onClick={() => navigateProject('next')}
              className="fixed right-4 top-1/2 -translate-y-1/2 text-neon-solar hover:text-tomato-red transition-colors p-2 nav-arrow-glow"
              aria-label="Próximo projeto"
              style={{ zIndex: 60 }}
            >
              <ChevronRight size={32} />
            </button>

            {/* Hero image 16:9 with border */}
            <div className="relative w-full" style={{ aspectRatio: '16/9' }}>
              <img
                src={currentProject.image}
                alt={currentProject.title}
                className="w-full h-full object-cover border-4"
                style={{ borderColor: '#FCEC07' }}
                loading="lazy"
              />
            </div>

            {/* Content area */}
            <div className="p-6 md:p-8">
              {/* Project title with underline */}
              <h2
                className="font-gt-flexa text-3xl mb-8 text-center project-title-underline"
                style={{
                  color: '#FCEC07',
                  fontSize: '32px'
                }}
              >
                {currentProject.title}
              </h2>

              {/* Content cards */}
              <div className="space-y-6">
                {/* DESAFIO Card */}
                <div className="bg-dark-bg p-6 rounded-lg" style={{ marginBlock: '24px' }}>
                  <h3
                    className="font-gt-flexa mb-4 subtitle-hover cursor-pointer"
                    style={{
                      color: '#FF3B30',
                      fontSize: '20px',
                      letterSpacing: '0.5px'
                    }}
                  >
                    DESAFIO
                  </h3>
                  <p
                    className="text-white"
                    style={{
                      fontSize: '16px',
                      lineHeight: '1.6'
                    }}
                  >
                    {currentProject.challenge}
                  </p>
                </div>

                {/* PROCESSO Card */}
                <div className="bg-dark-bg p-6 rounded-lg" style={{ marginBlock: '24px' }}>
                  <h3
                    className="font-gt-flexa mb-4 subtitle-hover cursor-pointer"
                    style={{
                      color: '#FF3B30',
                      fontSize: '20px',
                      letterSpacing: '0.5px'
                    }}
                  >
                    PROCESSO
                  </h3>
                  <p
                    className="text-white"
                    style={{
                      fontSize: '16px',
                      lineHeight: '1.6'
                    }}
                  >
                    {currentProject.process}
                  </p>
                </div>

                {/* IMPACTO Card */}
                <div className="bg-dark-bg p-6 rounded-lg" style={{ marginBlock: '24px' }}>
                  <h3
                    className="font-gt-flexa mb-4 subtitle-hover cursor-pointer"
                    style={{
                      color: '#FF3B30',
                      fontSize: '20px',
                      letterSpacing: '0.5px'
                    }}
                  >
                    IMPACTO
                  </h3>
                  <p
                    className="text-white"
                    style={{
                      fontSize: '16px',
                      lineHeight: '1.6'
                    }}
                  >
                    {currentProject.impact}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div
            className="modal-progress"
            style={{ width: `${modalProgress}%` }}
          ></div>
        </div>
      )}
    </>
  );
};

export default Portfolio;
