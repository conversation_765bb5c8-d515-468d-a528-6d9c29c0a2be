import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * Contains all of `recommended`, as well as additional strict rules that can also catch bugs.
 * @see {@link https://typescript-eslint.io/users/configs#strict}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=strict.d.ts.map