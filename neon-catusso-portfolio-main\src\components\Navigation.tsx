
import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from 'lucide-react';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    { to: '/', label: 'HOME' },
    { to: '/projetos', label: 'PROJETOS' },
    { to: '/sobre', label: 'SOBRE' },
    { to: '/servicos', label: 'SERVIÇOS' },
    { to: '/contato', label: 'CONTATO' },
  ];

  const toggleMenu = () => setIsOpen(!isOpen);

  return (
    <>
      {/* Logo tipográfico */}
      <div className="fixed top-4 left-4 z-50">
        <NavLink 
          to="/" 
          className="font-gt-flexa text-2xl text-neon-solar cursor-custom focus:outline-none focus:ring-2 focus:ring-neon-solar focus:ring-offset-2 focus:ring-offset-dark-bg"
          aria-label="Voltar para página inicial"
        >
          FC.
        </NavLink>
      </div>

      {/* Desktop Navigation */}
      <nav className="hidden md:block fixed left-4 top-1/2 transform -translate-y-1/2 z-50">
        <div className="bg-dark-bg border-2 border-neon-solar p-4 space-y-2">
          {navItems.map((item) => (
            <NavLink
              key={item.to}
              to={item.to}
              className={({ isActive }) =>
                `block font-gt-flexa text-sm px-4 py-2 transition-colors cursor-custom focus:outline-none focus:ring-2 focus:ring-neon-solar focus:ring-offset-2 focus:ring-offset-dark-bg ${
                  isActive
                    ? 'text-dark-bg bg-neon-solar border-2 border-neon-solar'
                    : 'text-text-white hover:text-neon-solar border-2 border-transparent'
                }`
              }
              aria-label={`Navegar para ${item.label}`}
            >
              {item.label}
            </NavLink>
          ))}
        </div>
      </nav>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        <Button
          onClick={toggleMenu}
          className="fixed top-4 right-4 z-50 bg-dark-bg text-neon-solar border-2 border-neon-solar hover:bg-neon-solar hover:text-dark-bg cursor-custom focus:outline-none focus:ring-2 focus:ring-neon-solar focus:ring-offset-2 focus:ring-offset-dark-bg"
          size="sm"
          aria-label={isOpen ? 'Fechar menu' : 'Abrir menu'}
        >
          {isOpen ? (
            <X size={20} className="transition-transform duration-300 rotate-0" />
          ) : (
            <Menu size={20} className="transition-transform duration-300" />
          )}
        </Button>

        {/* Mobile Menu Overlay */}
        {isOpen && (
          <div className="fixed inset-0 bg-dark-bg z-40 flex items-center justify-center">
            <nav className="space-y-6">
              {navItems.map((item) => (
                <NavLink
                  key={item.to}
                  to={item.to}
                  onClick={() => setIsOpen(false)}
                  className={({ isActive }) =>
                    `block font-gt-flexa text-2xl text-center px-6 py-3 transition-colors cursor-custom focus:outline-none focus:ring-2 focus:ring-neon-solar focus:ring-offset-2 focus:ring-offset-dark-bg ${
                      isActive
                        ? 'text-dark-bg bg-neon-solar border-2 border-neon-solar'
                        : 'text-text-white hover:text-neon-solar border-2 border-transparent'
                    }`
                  }
                  aria-label={`Navegar para ${item.label}`}
                >
                  {item.label}
                </NavLink>
              ))}
            </nav>
          </div>
        )}
      </div>
    </>
  );
};

export default Navigation;
