
import { Button } from "@/components/ui/button";

const FixedCTA = () => {
  const openWhatsApp = () => {
    window.open('https://wa.me/5511999999999?text=Olá! Gostaria de agendar um briefing.', '_blank');
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      <div className="diagonal-strip bg-tomato-red py-6 px-4">
        <div className="container mx-auto flex flex-col sm:flex-row items-center justify-between gap-4 transform skew-y-0">
          <div className="text-center sm:text-left">
            <h3 className="font-gt-flexa text-text-white text-2xl md:text-3xl">
              VAMOS CONVERSAR?
            </h3>
            <p className="text-text-white text-sm md:text-base">
              Agende um briefing gratuito e vamos criar algo incrível juntos!
            </p>
          </div>
          
          <Button
            onClick={openWhatsApp}
            className="bg-text-white text-tomato-red hover:bg-gray-100 font-gt-flexa text-lg px-8 py-3 cursor-custom whitespace-nowrap"
          >
            FALE COMIGO
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FixedCTA;
