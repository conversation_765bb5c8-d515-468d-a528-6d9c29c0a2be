
import Navigation from "@/components/Navigation";
import ThemeToggle from "@/components/ThemeToggle";

const Projetos = () => {
  const projects = [
    {
      id: 1,
      title: "E-commerce Fashion",
      category: "UX/UI Design",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop",
      description: "Redesign completo de plataforma de moda com foco em conversão"
    },
    {
      id: 2,
      title: "App Fintech",
      category: "Mobile Design",
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=600&h=400&fit=crop",
      description: "Interface intuitiva para gestão financeira pessoal"
    },
    {
      id: 3,
      title: "Brand Identity Tech",
      category: "Branding",
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop",
      description: "Identidade visual completa para startup de tecnologia"
    },
    {
      id: 4,
      title: "Dashboard SaaS",
      category: "Web Design",
      image: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=600&h=400&fit=crop",
      description: "Interface administrativa para plataforma B2B"
    },
    {
      id: 5,
      title: "Food Delivery",
      category: "UX/UI Design",
      image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=600&h=400&fit=crop",
      description: "App de delivery com experiência gamificada"
    },
    {
      id: 6,
      title: "Portfolio Arquiteto",
      category: "Web Design",
      image: "https://images.unsplash.com/photo-1503387762-592deb58ef4e?w=600&h=400&fit=crop",
      description: "Site portfolio minimalista para arquiteto"
    }
  ];

  const categories = ["Todos", "UX/UI Design", "Branding", "Web Design", "Mobile Design"];

  return (
    <div className="min-h-screen bg-white dark:bg-dark-bg">
      <Navigation />
      <ThemeToggle />
      
      <div className="container mx-auto px-4 py-16 md:pl-32">
        <header className="mb-12">
          <h1 className="font-gt-flexa text-4xl md:text-6xl text-dark-bg dark:text-neon-solar mb-4">
            MEUS PROJETOS
          </h1>
          <p className="text-lg text-dark-bg dark:text-text-white max-w-2xl">
            Uma seleção dos meus trabalhos mais recentes em UX/UI, branding e design digital.
          </p>
        </header>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-4 mb-8">
          {categories.map((category) => (
            <button
              key={category}
              className="px-4 py-2 font-gt-flexa text-sm border-2 border-dark-bg dark:border-neon-solar text-dark-bg dark:text-neon-solar hover:bg-neon-solar hover:text-dark-bg transition-colors cursor-custom"
            >
              {category}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => (
            <div
              key={project.id}
              className="neo-brutal-card group cursor-custom"
            >
              <div className="overflow-hidden mb-4">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  loading="lazy"
                />
              </div>
              <div className="p-4">
                <div className="text-sm text-tomato-red font-space-grotesk font-medium mb-2">
                  {project.category}
                </div>
                <h3 className="font-gt-flexa text-xl text-dark-bg mb-2">
                  {project.title}
                </h3>
                <p className="text-dark-bg text-sm">
                  {project.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Projetos;
