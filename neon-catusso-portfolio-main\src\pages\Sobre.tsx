import Navigation from "@/components/Navigation";
import ThemeToggle from "@/components/ThemeToggle";
const Sobre = () => {
  const skills = ["User Experience (UX)", "User Interface (UI)", "Design Systems", "Prototipagem", "User Research", "Information Architecture", "Wireframing", "Visual Design", "Branding", "Motion Design"];
  const tools = ["Figma", "Adobe Creative Suite", "Sketch", "InVision", "Principle", "Framer", "Miro", "Notion", "Webflow", "HTML/CSS"];
  return <div className="min-h-screen bg-white dark:bg-dark-bg">
      <Navigation />
      <ThemeToggle />
      
      <div className="container mx-auto px-4 py-16 md:pl-32">
        <header className="mb-12">
          <h1 className="font-gt-flexa text-4xl md:text-6xl text-dark-bg dark:text-neon-solar mb-4">
            SOBRE MIM
          </h1>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Bio Section */}
          <div>
            <div className="mb-8 rounded-none">
              <img alt="Fábio Catusso" loading="lazy" src="/lovable-uploads/7798ff01-0a26-45c4-b6e1-f606aeb767ea.jpg" className="w-48 h-48 rounded-full border-4 border-tomato-red mx-auto lg:mx-0 mb-6 object-cover" />
            </div>
            
            <div className="space-y-4 text-dark-bg dark:text-text-white text-lg leading-relaxed">
              <p>
                Olá! Sou Fábio Catusso, designer UX/UI com mais de 8 anos de experiência 
                criando experiências digitais que conectam pessoas e marcas de forma 
                significativa.
              </p>
              <p>
                Nascido em São Paulo, sempre fui fascinado pela intersecção entre 
                tecnologia e design. Minha jornada começou no design gráfico, mas 
                rapidamente me apaixonei pelo universo digital e suas infinitas 
                possibilidades.
              </p>
              <p>
                Acredito que o bom design não é apenas sobre estética - é sobre 
                resolver problemas reais e criar soluções que fazem sentido para 
                as pessoas que as usam. Cada projeto é uma oportunidade de impactar 
                positivamente a vida de alguém.
              </p>
            </div>
          </div>

          {/* Experience Timeline */}
          <div>
            <h2 className="font-gt-flexa text-2xl text-dark-bg dark:text-neon-solar mb-6">
              EXPERIÊNCIA
            </h2>
            <div className="space-y-6">
              <div className="border-l-2 border-neon-solar pl-4">
                <div className="text-sm text-tomato-red font-medium">2020 - Presente</div>
                <h3 className="font-gt-flexa text-lg text-dark-bg dark:text-text-white">
                  Senior UX/UI Designer
                </h3>
                <p className="text-dark-bg dark:text-text-white">
                  Designer freelancer especializado em produtos digitais para startups e empresas de tecnologia.
                </p>
              </div>
              
              <div className="border-l-2 border-neon-solar pl-4">
                <div className="text-sm text-tomato-red font-medium">2018 - 2020</div>
                <h3 className="font-gt-flexa text-lg text-dark-bg dark:text-text-white">
                  Product Designer
                </h3>
                <p className="text-dark-bg dark:text-text-white">
                  TechCorp - Liderança de design para produtos B2B com mais de 50k usuários ativos.
                </p>
              </div>
              
              <div className="border-l-2 border-neon-solar pl-4">
                <div className="text-sm text-tomato-red font-medium">2016 - 2018</div>
                <h3 className="font-gt-flexa text-lg text-dark-bg dark:text-text-white">
                  UX Designer
                </h3>
                <p className="text-dark-bg dark:text-text-white">
                  Digital Agency - Criação de interfaces para e-commerce e aplicativos mobile.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Skills & Tools */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div>
            <h2 className="font-gt-flexa text-2xl text-dark-bg dark:text-neon-solar mb-6">
              SKILLS
            </h2>
            <div className="grid grid-cols-2 gap-3">
              {skills.map(skill => <div key={skill} className="bg-neon-solar text-dark-bg font-space-grotesk font-medium text-sm px-3 py-2 text-center cursor-custom">
                  {skill}
                </div>)}
            </div>
          </div>

          <div>
            <h2 className="font-gt-flexa text-2xl text-dark-bg dark:text-neon-solar mb-6">
              FERRAMENTAS
            </h2>
            <div className="grid grid-cols-2 gap-3">
              {tools.map(tool => <div key={tool} className="border-2 border-dark-bg dark:border-neon-solar text-dark-bg dark:text-neon-solar font-space-grotesk font-medium text-sm px-3 py-2 text-center hover:bg-neon-solar hover:text-dark-bg transition-colors cursor-custom">
                  {tool}
                </div>)}
            </div>
          </div>
        </div>
      </div>
    </div>;
};
export default Sobre;