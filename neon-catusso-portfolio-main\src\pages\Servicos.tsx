
import Navigation from "@/components/Navigation";
import ThemeToggle from "@/components/ThemeToggle";
import { Button } from "@/components/ui/button";

const Servicos = () => {
  const services = [
    {
      title: "UX/UI Design",
      price: "A partir de R$ 3.500",
      duration: "2-4 semanas",
      description: "Design completo de interfaces digitais com foco na experiência do usuário.",
      includes: [
        "Research e análise de usuários",
        "Wireframes e protótipos",
        "UI Design e sistema visual",
        "Testes de usabilidade",
        "Handoff para desenvolvimento"
      ]
    },
    {
      title: "Branding Digital",
      price: "A partir de R$ 2.800",
      duration: "3-5 semanas",
      description: "Criação ou redesign de identidade visual para marcas digitais.",
      includes: [
        "Estratégia de marca",
        "Logo e identidade visual",
        "Manual de marca",
        "Aplicações digitais",
        "Social media templates"
      ]
    },
    {
      title: "Design System",
      price: "A partir de R$ 4.500",
      duration: "4-6 semanas",
      description: "Sistema de design escalável para produtos digitais complexos.",
      includes: [
        "Auditoria de interface",
        "Tokens de design",
        "Biblioteca de componentes",
        "Documentação completa",
        "Treinamento da equipe"
      ]
    }
  ];

  const processSteps = [
    {
      number: "01",
      title: "Briefing",
      description: "Conversa inicial para entender suas necessidades e objetivos do projeto."
    },
    {
      number: "02", 
      title: "Proposta",
      description: "Elaboração de proposta detalhada com escopo, cronograma e investimento."
    },
    {
      number: "03",
      title: "Desenvolvimento",
      description: "Execução do projeto com entregas parciais e feedback contínuo."
    },
    {
      number: "04",
      title: "Entrega",
      description: "Entrega final com todos os arquivos e documentação necessária."
    }
  ];

  const openWhatsApp = () => {
    window.open('https://wa.me/5511999999999?text=Olá! Gostaria de conversar sobre um projeto.', '_blank');
  };

  return (
    <div className="min-h-screen bg-white dark:bg-dark-bg">
      <Navigation />
      <ThemeToggle />
      
      <div className="container mx-auto px-4 py-16 md:pl-32">
        <header className="mb-12">
          <h1 className="font-gt-flexa text-4xl md:text-6xl text-dark-bg dark:text-neon-solar mb-4">
            SERVIÇOS
          </h1>
          <p className="text-lg text-dark-bg dark:text-text-white max-w-2xl">
            Soluções em design digital para transformar sua ideia em experiências memoráveis.
          </p>
        </header>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {services.map((service, index) => (
            <div
              key={index}
              className="neo-brutal-card p-6 h-full flex flex-col"
            >
              <h3 className="font-gt-flexa text-xl text-dark-bg mb-2">
                {service.title}
              </h3>
              <div className="text-tomato-red font-space-grotesk font-bold text-lg mb-1">
                {service.price}
              </div>
              <div className="text-dark-bg text-sm mb-4">
                {service.duration}
              </div>
              <p className="text-dark-bg mb-4 flex-grow">
                {service.description}
              </p>
              <div className="space-y-2">
                <h4 className="font-gt-flexa text-sm text-dark-bg">INCLUI:</h4>
                <ul className="space-y-1">
                  {service.includes.map((item, idx) => (
                    <li key={idx} className="text-dark-bg text-sm flex items-start">
                      <span className="text-neon-solar mr-2">•</span>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Process Section */}
        <section className="mb-16">
          <h2 className="font-gt-flexa text-3xl text-dark-bg dark:text-neon-solar mb-8">
            COMO FUNCIONA
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {processSteps.map((step) => (
              <div
                key={step.number}
                className="bg-neon-solar p-6 text-center"
              >
                <div className="font-gt-flexa text-3xl text-dark-bg mb-2">
                  {step.number}
                </div>
                <h3 className="font-gt-flexa text-lg text-dark-bg mb-3">
                  {step.title}
                </h3>
                <p className="text-dark-bg text-sm">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center bg-gray-100 dark:bg-dark-bg border-2 border-neon-solar p-8">
          <h2 className="font-gt-flexa text-2xl text-dark-bg dark:text-neon-solar mb-4">
            PRONTO PARA COMEÇAR?
          </h2>
          <p className="text-dark-bg dark:text-text-white mb-6 max-w-lg mx-auto">
            Vamos conversar sobre seu projeto e criar algo incrível juntos.
          </p>
          <Button
            onClick={openWhatsApp}
            className="bg-tomato-red text-text-white hover:bg-red-600 font-gt-flexa text-lg px-8 py-3 cursor-custom"
          >
            FALAR COMIGO
          </Button>
        </section>
      </div>
    </div>
  );
};

export default Servicos;
