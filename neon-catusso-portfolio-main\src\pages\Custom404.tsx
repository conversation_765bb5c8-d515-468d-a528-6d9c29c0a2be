
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import ThemeToggle from "@/components/ThemeToggle";

const Custom404 = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-white dark:bg-dark-bg flex items-center justify-center">
      <ThemeToggle />
      
      <div className="text-center px-4">
        <div className="mb-8">
          <h1 className="font-gt-flexa text-6xl md:text-9xl text-neon-solar neon-glow mb-4">
            404
          </h1>
          <div className="w-32 h-1 bg-tomato-red mx-auto mb-6"></div>
        </div>

        <h2 className="font-gt-flexa text-2xl md:text-3xl text-dark-bg dark:text-text-white mb-4">
          PÁGINA NÃO ENCONTRADA
        </h2>
        
        <p className="text-dark-bg dark:text-text-white text-lg mb-8 max-w-md mx-auto">
          Ops! Parece que você se perdeu no universo digital. 
          A página que você procura não existe mais ou foi movida.
        </p>

        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <Button
            onClick={() => navigate('/')}
            className="bg-neon-solar text-dark-bg hover:bg-yellow-400 font-gt-flexa px-8 py-3 cursor-custom"
          >
            VOLTAR AO INÍCIO
          </Button>
          
          <Button
            onClick={() => navigate('/projetos')}
            className="bg-transparent border-2 border-tomato-red text-tomato-red hover:bg-tomato-red hover:text-text-white font-gt-flexa px-8 py-3 cursor-custom"
          >
            VER PROJETOS
          </Button>
        </div>

        {/* Decorative elements */}
        <div className="mt-12 relative">
          <div className="absolute inset-0 flex items-center justify-center opacity-10">
            <div className="w-64 h-64 border-2 border-neon-solar rotate-45"></div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center opacity-5">
            <div className="w-32 h-32 bg-tomato-red rotate-12"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Custom404;
