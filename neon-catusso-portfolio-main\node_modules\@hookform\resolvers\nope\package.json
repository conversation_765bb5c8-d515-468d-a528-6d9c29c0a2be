{"name": "@hookform/resolvers/nope", "amdName": "hookformResolversNope", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: nope", "main": "dist/nope.js", "module": "dist/nope.module.js", "umd:main": "dist/nope.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.0.0", "@hookform/resolvers": "^2.0.0", "nope-validator": "^0.12.0"}}